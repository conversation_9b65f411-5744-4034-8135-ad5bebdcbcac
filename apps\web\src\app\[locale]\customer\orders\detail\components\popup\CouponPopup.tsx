'use client'

import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { OrderPickupInfo, OrderVouchers } from '@ninebot/core'
import { Button, Carousel } from 'antd'
import { CarouselRef } from 'antd/es/carousel'

import { CustomImage, IconArrow, Modal } from '@/components'

// 扩展券码类型，增加使用门店和使用时间
interface ExtendedVoucher {
  store_name?: string
  used_time?: string
  code?: string | null
  qr_code?: string | null
  status?: string | null
  status_label?: string | null
  expired_at?: string
}

type CouponPopupProps = {
  /** 是否显示 */
  popupVisible: boolean
  /** 拷贝到剪贴板 */
  copyToClipboard: (text: string) => void
  /** 关闭弹窗 */
  closePopup: () => void
  /** 自提取货码 */
  pickupInfo?: OrderPickupInfo
  couponList?: OrderVouchers
  /** 商品信息 */
  productInfo?: {
    name: string
    image: string
  }
}

/**
 * 券码弹窗
 */
const CouponPopup = (props: CouponPopupProps) => {
  const {
    popupVisible,
    copyToClipboard,
    closePopup,
    pickupInfo,
    couponList = [],
    productInfo,
  } = props
  const getI18nString = useTranslations('Common')
  const swiperRef = useRef<CarouselRef>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  /**
   * 判断当前是否为取货码模式
   */
  const isPickupCode = useMemo(() => {
    return !!pickupInfo
  }, [pickupInfo])

  /**
   * 获取当前显示的验证码
   */
  const currentCode = useMemo(() => {
    return isPickupCode ? pickupInfo?.code : couponList?.[currentIndex]?.code
  }, [isPickupCode, pickupInfo?.code, couponList, currentIndex])

  /**
   * 格式化 data
   */
  const carouselData = useMemo(() => {
    if (pickupInfo) {
      return [
        {
          image_url: pickupInfo?.qr_code,
          imageStyle: pickupInfo?.status === '0' ? {} : { opacity: 0.08 },
          children:
            pickupInfo?.status !== '0' ? (
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-[4px] border-[1px] border-[#DA291C] px-[32px] py-[8px]">
                <div className="whitespace-nowrap font-miSansDemiBold450 text-[12px] leading-[16px] text-[#DA291C]">
                  {pickupInfo?.status_label}
                </div>
              </div>
            ) : null,
        },
      ]
    } else {
      return couponList?.map((coupon) => {
        return {
          image_url: coupon?.qr_code,
          imageStyle: coupon?.status === '0' ? {} : { opacity: 0.08 },
          children:
            coupon?.status !== '0' ? (
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-[4px] border-[1px] border-[#DA291C] px-[32px] py-[8px]">
                <div className="whitespace-nowrap font-miSansDemiBold450 text-[12px] leading-[16px] text-[#DA291C]">
                  {coupon?.status_label}
                </div>
              </div>
            ) : null,
        }
      })
    }
  }, [pickupInfo, couponList])

  /**
   * 获取当前验证码的状态
   */
  const isUnUsed = useMemo(() => {
    if (isPickupCode) {
      return pickupInfo?.status === '0'
    }
    return couponList?.[currentIndex]?.status === '0'
  }, [isPickupCode, pickupInfo?.status, couponList, currentIndex])

  /**
   * 获取当前券码
   */
  const getCurrentCoupon = useMemo((): ExtendedVoucher | undefined => {
    if (isPickupCode) {
      return pickupInfo as unknown as ExtendedVoucher
    } else {
      return couponList?.[currentIndex] as unknown as ExtendedVoucher
    }
  }, [isPickupCode, pickupInfo, couponList, currentIndex])

  /**
   * 处理轮播图切换动画
   */
  const handleSwipeAnimation = () => {
    setIsAnimating(true)
    setTimeout(() => setIsAnimating(false), 300) // 动画结束后重置状态
  }

  /**
   * 切换到上一张
   */
  const handlePrevPress = () => {
    if (currentIndex > 0 && !isAnimating) {
      try {
        const newIndex = Math.max(currentIndex - 1, 0)
        setCurrentIndex(newIndex)
        swiperRef.current?.goTo(newIndex, false)
        handleSwipeAnimation()
      } catch (error) {
        console.error('切换上一张失败:', error)
      }
    }
  }

  /**
   * 切换到下一张
   */
  const handleNextPress = () => {
    if (currentIndex < (couponList?.length || 0) - 1 && !isAnimating) {
      try {
        const newIndex = Math.min(currentIndex + 1, (couponList?.length || 0) - 1)
        setCurrentIndex(newIndex)
        swiperRef.current?.goTo(newIndex, false)
        handleSwipeAnimation()
      } catch (error) {
        console.error('切换下一张失败:', error)
      }
    }
  }

  useEffect(() => {
    if (!popupVisible) {
      swiperRef.current?.goTo(0)
      setCurrentIndex(0)
    }
  }, [popupVisible])

  return (
    <Modal
      isOpen={popupVisible}
      width={600}
      onClose={closePopup}
      okButtonProps={{ style: { display: 'none' } }}
      cancelButtonProps={{ style: { display: 'none' } }}
      title={getI18nString('coupon_code')}>
      <>
        <div className="-mt-base-16 mb-base-16 h-[1px] w-full bg-[#F3F3F4]"></div>

        <div className="mx-auto w-[420px]">
          {productInfo && (
            <div className="mb-[16px] flex items-center">
              <CustomImage
                width={100}
                height={100}
                className="mr-[16px] rounded-[4px]"
                src={productInfo.image}
                alt={productInfo.name}
              />
              <div className="font-miSansDemiBold450 text-[16px] leading-[22px] text-[#000000]">
                {productInfo.name}
              </div>

              <div className="pb-[24px] font-miSansDemiBold450 text-[16px] leading-[22px] text-[#000000]">
                {getI18nString('verification_code_total')}
                <span className="text-[#DA291C]">{isPickupCode ? '1' : couponList?.length}</span>
                {getI18nString('piece')}
              </div>
            </div>
          )}

          <div className="flex flex-col items-center rounded-base-12 bg-gray-base">
            <div className="relative w-full py-[16px]">
              <div className="absolute -bottom-[8px] -left-[8px] h-[16px] w-[16px] rounded-full bg-white" />
              <div className="absolute -bottom-[8px] -right-[8px] h-[16px] w-[16px] rounded-full bg-white" />
              <div className="flex items-center">
                <div className="min-w-[76px] px-[12px] py-[18px] text-center text-[14px] leading-[20px] text-[#0F0F0F]">
                  {getI18nString('verification_code_index', { key: currentIndex + 1 })}
                </div>
                <div className="h-[56px] w-[1px] border-l border-dashed border-[#E1E1E4] bg-transparent" />
                <div className="flex flex-1 items-center justify-between px-8">
                  <div
                    className={
                      isUnUsed
                        ? 'font-miSansRegular330 text-[16px] leading-[22px] text-[#86868B]'
                        : 'font-miSansRegular330 text-[16px] leading-[22px] text-[#AEAEB2] line-through'
                    }>
                    {currentCode}{' '}
                  </div>
                  <Button
                    className="ml-[8px] rounded-[24px] border border-[#E5E5EA] px-[16px] py-[4px]"
                    onClick={() => copyToClipboard(currentCode || '')}>
                    <div
                      className={
                        isUnUsed
                          ? 'font-miSansRegular330 text-[16px] leading-[22px] text-[#0F0F0F]'
                          : 'font-miSansRegular330 text-[16px] leading-[22px] text-[#AEAEB2]'
                      }>
                      {getI18nString('copy')}
                    </div>
                  </Button>
                </div>
              </div>

              {!isPickupCode && (
                <>
                  <div className="mt-[8px] font-miSansDemiBold450 text-[14px] leading-[20px] text-[#8E8E93]">
                    {getI18nString('expired_time_until')}
                    {couponList?.[currentIndex]?.expired_at?.split(' ')[0]}
                  </div>
                  {!isUnUsed && getCurrentCoupon?.store_name && (
                    <div className="mt-[8px]">
                      <div className="font-miSansDemiBold450 text-[14px] leading-[20px] text-[#8E8E93]">
                        使用门店：{getCurrentCoupon.store_name}
                      </div>
                      {getCurrentCoupon.used_time && (
                        <div className="font-miSansDemiBold450 text-[14px] leading-[20px] text-[#8E8E93]">
                          使用时间：{getCurrentCoupon.used_time}
                        </div>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>

            <div className="h-[1px] w-[381px] border-b border-dashed border-[#E1E1E4] bg-transparent" />

            <div className="mt-[24px] w-full flex-1">
              <div className="flex flex-row items-center justify-center">
                {Number(carouselData?.length) > 1 ? (
                  <button onClick={handlePrevPress}>
                    <IconArrow rotate={90} />
                  </button>
                ) : null}

                <div className="mx-auto h-[180px] w-[180px]">
                  <Carousel
                    ref={swiperRef}
                    dots={false}
                    beforeChange={(from, to) => {
                      console.log('轮播即将从', from, '切换到', to)
                      setCurrentIndex(to)
                    }}
                    afterChange={(current) => {
                      console.log('轮播已切换到', current)
                      setCurrentIndex(current)
                    }}>
                    {carouselData?.map((coupon, index) => {
                      // 获取当前券码的状态
                      const currentCouponStatus = isPickupCode
                        ? pickupInfo?.status
                        : couponList?.[index]?.status
                      const isCurrentUnused = currentCouponStatus === '0'

                      return (
                        <div key={index} className="relative">
                          <CustomImage
                            displayMode="responsive"
                            src={coupon.image_url || ''}
                            className={`h-full w-full ${!isCurrentUnused ? 'opacity-[0.08]' : ''}`}
                            style={{ ...coupon.imageStyle }}
                            alt=""
                          />
                          {coupon.children}
                        </div>
                      )
                    })}
                  </Carousel>
                </div>

                {Number(carouselData?.length) > 1 ? (
                  <button onClick={handleNextPress}>
                    <IconArrow rotate={-90} />
                  </button>
                ) : null}
              </div>
              <div className="mb-16 mt-[8px] text-center font-miSansDemiBold450 text-[14px] leading-[17px] text-[#000000]">
                {isPickupCode ? '1/1' : `${currentIndex + 1}/${couponList?.length}`}
              </div>

              {!!pickupInfo?.status_label && !!pickupInfo?.expired_info && (
                <div className="my-8 text-center font-miSansDemiBold450 text-[12px] leading-[17px]">
                  {pickupInfo?.expired_info}
                </div>
              )}
            </div>
          </div>
        </div>
      </>
    </Modal>
  )
}

export default CouponPopup
